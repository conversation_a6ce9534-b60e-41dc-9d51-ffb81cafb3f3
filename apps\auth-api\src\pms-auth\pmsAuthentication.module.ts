import { Module } from '@nestjs/common';
import { HttpModule } from 'packages/http';
import { AuthenticationController } from './pmsAuthentication.controller';
import { AuthenticationService } from './pmsAuthentication.service';
import { AuthService } from '../auth/auth.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProfile } from '../entities/userProfile.entity';
import { User } from '../entities/user.entity';
import { Token } from '../entities/token.entity';
import { SyncAccountsService } from '../schedulers/syncScheduler';
@Module({
  imports: [ TypeOrmModule.forFeature([ UserProfile, User, Token ]),
  HttpModule.register({ baseURL: process.env.PMS_API_URL })],
  controllers: [AuthenticationController],
  providers: [AuthenticationService, AuthService, SyncAccountsService],
})
export class AuthenticationModule {}
