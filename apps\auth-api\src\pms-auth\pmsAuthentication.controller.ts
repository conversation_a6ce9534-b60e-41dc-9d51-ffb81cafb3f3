/* eslint-disable prefer-const */
/* eslint-disable prettier/prettier */
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { AuthenticateViewModelModel } from './authentication/authenticateViewModel.model';
import { DeleteSpouseViewModelModel } from './authentication/deleteSpouseViewModel.model';
import { OtpByPassViewModelModel } from './authentication/otpByPassViewModel.model';
import { ParentSignupViewModelModel } from './authentication/parentSignupViewModel.model';
import { VerifySpouseMobileDataModel } from './authentication/verifySpouseMobileData.model';
import { AuthenticationService } from './pmsAuthentication.service';
import { AuthService } from '../auth/auth.service';
@Controller()
export class AuthenticationController {
  constructor(
    private readonly service: AuthenticationService,
    private readonly authService: AuthService,
  ) {}

  @Get('authenticate/testNotification2')
  public async testNotification2(): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.service.testNotification2();
    return response;
  }

  @Post('authenticate/testNotification')
  public async testNotification(
    @Body() body: AuthenticateViewModelModel,
  ): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.service.testNotification(body);
    return response;
  }

  @Post('authenticate/authenticateParentViaPassword')
  public async authenticateParentViaPassword(
    @Body() body: AuthenticateViewModelModel,
  ): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.service.authenticateParentViaPassword(body);
    return response;
  }

  @Get('authenticate/logOutParent')
  public async logOutParent(): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.service.logOutParent();
    return response;
  }

  @Get('authenticate/sendParentOTP')
  public async sendParentOTP(
    @Query('phoneNumber') phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.sendParentOTP(phoneId);
    return response;
  }

  @Get('authenticate/sendParentEmailOTP')
  public async sendParentEmailOTP(
    emailId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.sendParentEmailOTP(emailId);
    return response;
  }

  @Post('authenticate/authenticateParentViaOTP')
  public async authenticateParentViaOTP(
    @Body() body: OtpByPassViewModelModel,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.authenticateParentViaOTP(body);
    return response;
  }

  @Get('authenticate/verifyParentNumber')
  public async verifyParentNumber(
    requestId?: string,
    oTP?: string,
  ): Promise<VerifySpouseMobileDataModel> {
    // -- not used
    const response = await this.service.verifyParentNumber(requestId, oTP);
    return response;
  }

  @Get('authenticate/authenticateParentViaEmailOTP')
  public async authenticateParentViaEmailOTP(
    emailId?: string,
    oTP?: string,
  ): Promise<AuthenticateViewModelModel> {
    // -- not used
    const response = await this.service.authenticateParentViaEmailOTP(
      emailId,
      oTP,
    );
    return response;
  }

  @Get('authenticate/sendParentMobileOTP')
  public async sendParentMobileOTP(
    @Query('phoneNumber') phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.sendParentMobileOTP(phoneId);
    return response;
  }

  @Get('authenticate/verifyParentMobileOTP')
  public async verifyParentMobileOTP(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('otp') otp?: string,
    @Query('requestId') requestId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.verifyParentMobileOTP(
      phoneId,
      otp,
      requestId,
    );
    return response;
  }

  @Get('authenticate/createSpouseMobileOTP')
  public async createSpouseMobileOTP(
    @Query('phoneNumber') phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.createSpouseMobileOTP(phoneId);
    return response;
  }

  @Post('authenticate/verifyCreateSpouseMobileOTP')
  public async verifyCreateSpouseMobileOTP(
    @Body() body: VerifySpouseMobileDataModel,
  ): Promise<VerifySpouseMobileDataModel> {
    // -- not used
    const response = await this.service.verifyCreateSpouseMobileOTP(body);
    return response;
  }

  @Get('authenticate/sendParentMobileOTPByPass')
  public async sendParentMobileOTPByPass(
    @Query('phoneNumber') phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.sendParentMobileOTPByPass(phoneId);
    return response;
  }

  @Get('authenticate/verifyParentMobileOTPByPass')
  public async verifyParentMobileOTPByPass(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('otp') otp?: string,
    @Query('requestId') requestId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.verifyParentMobileOTPByPass(
      phoneId,
      otp,
      requestId,
    );
    return response;
  }

  @Get('authenticate/sendParentEmailOTPByPass')
  public async sendParentEmailOTPByPass(
    email?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.sendParentEmailOTPByPass(email);
    return response;
  }

  @Get('authenticate/verifyParentEmailOTPByPass')
  public async verifyParentEmailOTPByPass(
    email?: string,
    otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.verifyParentEmailOTPByPass(email, otp);
    return response;
  }

  @Post('authenticate/authenticateParentViaOTPByPass')
  public async authenticateParentViaOTPByPass(
    @Body() body: OtpByPassViewModelModel,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.authenticateParentViaOTPByPass(body);
    return response;
  }

  @Get('authenticate/getSpousesExsitingDetails')
  public async getSpousesExistingDetails(
    @Query('phoneNo') phoneNo?: string,
    @Query('emiratesNo') emiratesNo?: string,
  ): Promise<AuthenticateViewModelModel[]> {
    // -- not used
    let phoneId = phoneNo ? `%2B${phoneNo}` : undefined;
    const response = await this.service.getSpousesExistingDetails(
      phoneId,
      emiratesNo,
    );
    return response;
  }

  @Post('authenticate/signUpVerifyAllByPass')
  public async signUpVerifyAllByPass(
    @Body() body: ParentSignupViewModelModel,
  ): Promise<any> {
    // If mobileNo is empty, generate a random Indian number
    if (!body.mobileNo || body.mobileNo.trim() === '') {
      // Generate a random 10-digit number, first digit not 0
      const randomTenDigits = Math.floor(1000000000 + Math.random() * 9000000000);
      body.mobileNo = `+91${randomTenDigits}`;
    } else {
      // Ensure it starts with +
      body.mobileNo = body.mobileNo.startsWith('+') ? body.mobileNo : `+${body.mobileNo}`;
    }
    // Generate a random 8-character alphanumeric password
    const randomPassword = Math.random().toString(36).slice(-8).replace(/[^a-zA-Z0-9]/g, '');
    body.password = randomPassword;
    // Call the PMS API for sign up
    const response: any = await this.service.signUpVerifyAllByPass(body);
    // If response contains JwtToken, PhoneNumber, and Id, call AuthService
    if (response && response.JwtToken && response.PhoneNumber && response.Id) {
      // Pass email if present
      const authPayload: any = {
        jwtToken: response.JwtToken,
        phoneNumber: response.PhoneNumber,
        parentId: response.Id,
      };
      if (body.email && body.email.trim() !== '') {
        authPayload.email = body.email;
      }
      const authResult = await this.authService.storeTokenAndGenerateNewTokenForParentForSignUp(authPayload);
      return {
        ...authResult,
        parentId: response.Id,
      };
    }
    // If not, just return the original response
    return response;
  }

  @Post('authenticate/signUpVerifyAll')
  public async signUpVerifyAll(
    @Body() body: ParentSignupViewModelModel,

  ): Promise<ParentSignupViewModelModel> {
    // Generate a random 8-character alphanumeric password
    const phoneId = body.mobileNo ? `+${body.mobileNo}` : undefined;
    body.mobileNo = phoneId;
    const randomPassword = Math.random().toString(36).slice(-8).replace(/[^a-zA-Z0-9]/g, '');
    body.password = randomPassword;
    // -- not used
    const response = await this.service.signUpVerifyAll(body);
    return response;
  }

  @Get('authenticate/sendParentSignUpMobileOTPByPass')
  public async sendParentSignUpMobileOTPByPass(
    @Query('phoneNumber') phoneNumber?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId  = `%2B${phoneNumber}`;
    const response =
      await this.service.sendParentSignUpMobileOTPByPass(phoneId);
    return response;
  }

  @Get('authenticate/sendParentSignUpMobileOTP')
  public async sendParentSignUpMobileOTP(
    @Query('phoneNumber') phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.sendParentSignUpMobileOTP(phoneId);
    return response;
  }

  @Get('authenticate/sendParentSignUpEmailOTP')
  public async sendParentSignUpEmailOTP(
    emailId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.sendParentSignUpEmailOTP(emailId);
    return response;
  }

  @Get('authenticate/sendParentSignUpEmailOTPByPass')
  public async sendParentSignUpEmailOTPByPass(
   @Query('emailId') emailId?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.sendParentSignUpEmailOTPByPass(emailId);
    return response;
  }

  @Get('authenticate/sendExistingParentEmailOTP')
  public async sendExistingParentEmailOTP(
    email?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.sendExistingParentEmailOTP(
      email,
      parentId,
    );
    return response;
  }

  @Get('authenticate/verifyExistingParentEmailOTP')
  public async verifyExistingParentEmailOTP(
    email?: string,
    otp?: string,
    parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.verifyExistingParentEmailOTP(
      email,
      otp,
      parentId,
    );
    return response;
  }

  @Get('authenticate/sendExistingParentMobileOTP')
  public async sendExistingParentMobileOTP(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('parentId') parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.sendExistingParentMobileOTP(
      phoneId,
      parentId,
    );
    return response;
  }

  @Get('authenticate/verifyExistingParentMobileOTP')
  public async verifyExistingParentMobileOTP(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('requestId') requestId?: string,
    @Query('otp') otp?: string,
    @Query('parentId') parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.verifyExistingParentMobileOTP(
      phoneId,
      requestId,
      otp,
      parentId,
    );
    return response;
  }

  @Get('authenticate/sendExistingParentMobileOTPByPass')
  public async sendExistingParentMobileOTPByPass(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('parentId') parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.sendExistingParentMobileOTPByPass(
      phoneId,
      parentId,
    );
    return response;
  }

  @Get('authenticate/verifyExistingParentMobileOTPByPass')
  public async verifyExistingParentMobileOTPByPass(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('requestId') requestId?: string,
    @Query('otp') otp?: string,
    @Query('parentId') parentId?: number,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.verifyExistingParentMobileOTPByPass(
      phoneId,
      requestId,
      otp,
      parentId,
    );
    return response;
  }

  @Get('authenticate/createSpouseMobileOTPByPass')
  public async createSpouseMobileOTPByPass(
    @Query('phoneNumber') phoneNumber?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.createSpouseMobileOTPByPass(phoneId);
    return response;
  }

  @Post('authenticate/verifyCreateSpouseMobileOTPByPass')
  public async verifyCreateSpouseMobileOTPByPass(
    @Body() body: VerifySpouseMobileDataModel,
  ): Promise<VerifySpouseMobileDataModel> {
    // -- not used
    const response = await this.service.verifyCreateSpouseMobileOTPByPass(body);
    return response;
  }

  @Get('authenticate/signupVerifyMobileOtp')
  public async signupVerifyMobileOtp(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('requestId') requestId?: string,
    @Query('otp') otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.signupVerifyMobileOtp(
      phoneId,
      requestId,
      otp,
    );
    return response;
  }

  @Get('authenticate/signupVerifyEmailOtp')
  public async signupVerifyEmailOtp(
    @Query('email') email?: string,
    @Query('otp') otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    const response = await this.service.signupVerifyEmailOtp(email, otp);
    return response;
  }

  @Get('authenticate/signupVerifyMobileOtpByPass')
  public async signupVerifyMobileOtpByPass(
    @Query('phoneNumber') phoneNumber?: string,
    @Query('requestId') requestId?: string,
    @Query('otp') otp?: string,
  ): Promise<OtpByPassViewModelModel> {
    // -- not used
    let phoneId = phoneNumber ? `%2B${phoneNumber}` : undefined;
    const response = await this.service.signupVerifyMobileOtpByPass(
      phoneId,
      requestId,
      otp,
    );
    return response;
  }

  @Get('authenticate/deleteSpouseMobileOTP')
  public async deleteSpouseMobileOTP(
    spouseId?: number,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.service.deleteSpouseMobileOTP(spouseId);
    return response;
  }

  @Post('authenticate/verifyDeleteSpouseMobileOTP')
  public async verifyDeleteSpouseMobileOTP(
    @Body() body: DeleteSpouseViewModelModel,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.service.verifyDeleteSpouseMobileOTP(body);
    return response;
  }

  @Get('authenticate/deleteSpouseMobileOTPByPass')
  public async deleteSpouseMobileOTPByPass(
    spouseId?: number,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.service.deleteSpouseMobileOTPByPass(spouseId);
    return response;
  }

  @Post('authenticate/verifyDeleteSpouseMobileOTPByPass')
  public async verifyDeleteSpouseMobileOTPByPass(
    @Body() body: DeleteSpouseViewModelModel,
  ): Promise<DeleteSpouseViewModelModel> {
    // -- not used
    const response = await this.service.verifyDeleteSpouseMobileOTPByPass(body);
    return response;
  }
}
