import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UserProfile } from './entities/userProfile.entity';
import { Token } from './entities/token.entity';
import { AuthApiController } from './auth-api.controller';
import { AuthApiService } from './auth-api.service';
import { UserProfileService } from './userProfileService/userProfile.service';
import { AuthenticationModule } from './pms-auth/pmsAuthentication.module';
import { AuthService } from './auth/auth.service';
import { HttpModule } from 'packages/http';
import { EtisalatService } from './auth/etisalat.servicee';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserProfile, Token]),
    HttpModule.register({}),
    AuthenticationModule,
  ],
  controllers: [AuthApiController],
  providers: [
    AuthApiService,
    UserProfileService,
    AuthService,
    // EtisalatService
  ],
})
export class AuthApiModule {}
